import { Auth } from "aws-amplify";
const UserPoolId = import.meta.env.VITE_APP_USER_POOL_ID;
import {
	AdminAddUserToGroupCommand,
	AdminUpdateUserAttributesCommand,
	AdminCreateUserCommand,
	AdminDeleteUserCommand,
	AdminRemoveUserFromGroupCommand,
	AdminSetUserMFAPreferenceCommand,
	CognitoIdentityProviderClient,
	ListGroupsCommand,
	ListUsersInGroupCommand,
	DeliveryMediumType,
} from "@aws-sdk/client-cognito-identity-provider";

import type {
	UserData,
	GroupRequestParams,
	Attributes,
	CreateUserData,
} from "@/types";
import { useBrandStore } from "@/stores/brand";

export default class UsersRepository {
	private client: CognitoIdentityProviderClient | null = null;

	async init(): Promise<void> {
		this.client = new CognitoIdentityProviderClient({
			region: import.meta.env.VITE_APP_AWS_REGION,
			credentials: await Auth.currentUserCredentials(),
		});
	}

	async fetchUsersInGroup(groupName: string): Promise<UserData[]> {
		let users: UserData[] = [];
		let response: any = {};

		const params: GroupRequestParams = {
			GroupName: groupName,
			UserPoolId: UserPoolId,
			Limit: 60,
		};

		const command = new ListUsersInGroupCommand(params);

		response = await this.client?.send(command);

		if (response) {
			users = [...users, ...response.Users];
		}

		while (response?.NextToken) {
			params.NextToken = response.NextToken;
			response = await this.client?.send(command);
			if (response) {
				users = [...users, ...response.Users];
			}
		}

		return users;
	}

	async listUsersWithGroups(): Promise<UserData[]> {
		const data = await this.listGroups();
		if (!data || !data.Groups) {
			return [];
		}

		const userGroupsList: UserData[] = [];

		for (const groupEntity of data.Groups) {
			if (!groupEntity.GroupName) continue;
			const usersInGroup = await this.fetchUsersInGroup(groupEntity.GroupName);

			for (const userEntity of usersInGroup) {
				const attributes = Array.isArray(userEntity.Attributes)
					? userEntity.Attributes
					: [];

				const emailAttribute = attributes.find(
					(attribute: Attributes) => attribute.Name === "email",
				);
				const brandsIdAttribute = attributes.find(
					(attribute: Attributes) => attribute.Name === "custom:brand_id",
				);
				const mfaRequiredAttribute = attributes.find(
					(attribute: Attributes) => attribute.Name === "custom:mfa_required",
				);

				userGroupsList.push({
					id: userEntity.Username,
					email: emailAttribute ? emailAttribute.Value : "",
					brand_ids: brandsIdAttribute ? brandsIdAttribute.Value : "",
					mfa_required: mfaRequiredAttribute ? mfaRequiredAttribute.Value : "",
					group: groupEntity.GroupName,
					created_at: userEntity.UserCreateDate,
					updated_at: userEntity.UserLastModifiedDate,
				});
			}
		}
		return userGroupsList;
	}

	listGroups() {
		const params = {
			UserPoolId,
		};
		const command = new ListGroupsCommand(params);
		return this.client?.send(command);
	}

	async createUser({ email, group, brandId, mfa }: CreateUserData) {
		try {
			if (!this.client) {
				throw new Error("Cognito client not initialized. Call init() first.");
			}

			const params = {
				UserPoolId,
				Username: email,
				DesiredDeliveryMediums: [DeliveryMediumType.EMAIL],
				UserAttributes: [
					{
						Name: "email",
						Value: email,
					},
					{
						Name: "email_verified",
						Value: "True",
					},
					{
						Name: "custom:brand_id",
						Value: brandId || "",
					},
					{
						Name: "custom:mfa_required",
						Value: mfa.toString(),
					},
				],
			};

			const command = new AdminCreateUserCommand(params);
			const user = await this.client.send(command);

			const addUserToGroupParams = {
				GroupName: group,
				UserPoolId,
				Username: email,
			};

			const commandAddUserToGroup = new AdminAddUserToGroupCommand(
				addUserToGroupParams,
			);
			await this.client.send(commandAddUserToGroup);

			return user;
		} catch (error) {
			console.error(error);
			throw error;
		}
	}

	async updateGroup(username: string, oldgroup: string, newgroup: string) {
		const params = {
			GroupName: oldgroup,
			UserPoolId,
			Username: username,
		};

		// Remove user from a group
		const commandDeleteUserToGroup = new AdminRemoveUserFromGroupCommand(
			params,
		);
		await this.client?.send(commandDeleteUserToGroup);

		const addUserToGroupParams = {
			GroupName: newgroup,
			UserPoolId,
			Username: username,
		};

		// Assign user to a group
		const commandUpdateUserToGroup = new AdminAddUserToGroupCommand(
			addUserToGroupParams,
		);
		await this.client?.send(commandUpdateUserToGroup);
	}

	async updateBrands(username: string, brands: string) {
		const params = {
			UserPoolId,
			Username: username,
			UserAttributes: [
				{
					Name: "custom:brand_id",
					Value: brands,
				},
			],
		};
		const commandUpdateUserAttributesCommand =
			new AdminUpdateUserAttributesCommand(params);
		await this.client?.send(commandUpdateUserAttributesCommand);
	}

	async updateMfa(username: string, mfa: string) {
		const params = {
			UserPoolId,
			Username: username,
			UserAttributes: [
				{
					Name: "custom:mfa_required",
					Value: mfa,
				},
			],
		};
		const commandUpdateUserAttributesCommand =
			new AdminUpdateUserAttributesCommand(params);
		await this.client?.send(commandUpdateUserAttributesCommand);

		if (mfa === "false") {
			const userMfaParams = {
				UserPoolId,
				Username: username,
				SMSMfaSettings: {
					Enabled: false,
					PreferredMfa: false,
				},
				SoftwareTokenMfaSettings: {
					Enabled: false,
					PreferredMfa: false,
				},
			};
			const updateMfaPreferences = new AdminSetUserMFAPreferenceCommand(
				userMfaParams,
			);
			await this.client?.send(updateMfaPreferences);
		}
	}

	async validateBrands(brandIds: Array<string>) {
		if (!brandIds) {
			return true;
		}
		const brandStore = useBrandStore();
		const allBrands = brandStore.$state.brands;

		console.log("Validating brand IDs:", brandIds);
		console.log("Available brands:", allBrands.map(b => b.brandInfo.id));

		const allBrandsAreNumerics = brandIds.every(
			(brandId) => !Number.isNaN(Number(brandId)),
		);
		const allBrandsExist = brandIds.every((brandId) =>
			allBrands.some((brand) => brand.brandInfo.id === parseInt(brandId)),
		);

		console.log("Are all numeric?", allBrandsAreNumerics);
		console.log("Do all exist?", allBrandsExist);

		return allBrandsAreNumerics && allBrandsExist;
	}

	async removeUser(userId: string) {
		const params = {
			UserPoolId,
			Username: userId,
		};
		const commandDeleteUserToGroup = new AdminDeleteUserCommand(params);
		await this.client?.send(commandDeleteUserToGroup);
	}
}

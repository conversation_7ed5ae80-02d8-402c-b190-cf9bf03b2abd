<script setup lang="ts">
import { computed, ref, nextTick, reactive } from "vue";
import { useRoute } from "vue-router";
import { useComparisonStore } from "@/stores/comparison";
import { useBrandStore } from "@/stores/brand";
import { storeToRefs } from "pinia";
import type { DateRangeType, BrandRatioObject } from "@/types";
import type { uiCheckBoxEvent, uiChartPointSelection } from "@/uiTypes";
import type { uiChartSelectedRangeEvent } from "@/uiTypes";
import {
  DocumentMagnifyingGlassIcon,
  IdentificationIcon,
  BuildingStorefrontIcon,
  FunnelIcon,
} from "@heroicons/vue/24/outline";
import { t } from "@/locales";
import { getStatValues } from "@/helpers/uiStats";
import {
  getSerie,
  getSerieParams,
  getBrandNames,
  getOptions,
  redirectFromComparisonToTable,
} from "@/helpers/uiChart";
import { calculateHeight } from "@/helpers/comparison";
import { getItemsWithStructure } from "@/helpers/uiRightSidebar";
import DateRange from "@/components/DateRange.vue";

const brandStore = useBrandStore();
const comparisonStore = useComparisonStore();

const route = useRoute();
const accountId = Number(route.params.account_id);

const {
  reservation,
  scan,
  checkin,
  previousReservation,
  previousScan,
  previousCheckin,
} = storeToRefs(comparisonStore);
const { getBrandsByAccountId } = storeToRefs(brandStore);

const brandIdsSelected = ref<number[]>([]);
const openRightSidebar = ref(false);
const createSafeOptions = () => ({
  chart: {
    type: "bar",
    toolbar: { show: false },
  },
  xaxis: {
    categories: [],
    labels: {
      hideOverlappingLabels: true,
    },
  },
  yaxis: {
    labels: {
      hideOverlappingLabels: true,
    },
  },
});

const optionsReservation = ref(createSafeOptions());
const optionsScan = ref(createSafeOptions());
const optionsCheckin = ref(createSafeOptions());
const renderComponent = ref(true);
const originalBrands = getBrandsByAccountId.value(accountId, null);
const brandIds = ref(<number[]>[]);
const brandsReservation = ref(<number[]>[]);
const brandsScan = ref(<number[]>[]);
const brandsCheckin = ref(<number[]>[]);

originalBrands.forEach(({ brandInfo: { id } }) => {
  brandIds.value.push(id);
  brandsReservation.value.push(id);
  brandsScan.value.push(id);
  brandsCheckin.value.push(id);
});

const uiChartHeight = ref(calculateHeight(brandIds.value.length));
const uiCheckBoxItem = ref(getItemsWithStructure(originalBrands));

const createIndependentRange = (value: DateRangeType): DateRangeType => {
  if (typeof value === "string") {
    return value;
  }
  return { ...value };
};

const comparisonDateRanges = reactive(<Record<string, DateRangeType>>{
  globalRange: createIndependentRange("24h"),
  scan: createIndependentRange("24h"),
  reservation: createIndependentRange("24h"),
  checkin: createIndependentRange("24h"),
});

const chartReservationSerie = computed(() => {
  if (!reservation.value || !Array.isArray(reservation.value)) {
    return [];
  }
  try {
    return getSerie(getSerieParams(reservation.value));
  } catch (error) {
    return [];
  }
});

const chartScanSerie = computed(() => {
  if (!scan.value || !Array.isArray(scan.value)) {
    return [];
  }
  try {
    return getSerie(getSerieParams(scan.value));
  } catch (error) {
    return [];
  }
});

const chartCheckinSerie = computed(() => {
  if (!checkin.value || !Array.isArray(checkin.value)) {
    return [];
  }
  try {
    return getSerie(getSerieParams(checkin.value));
  } catch (error) {
    return [];
  }
});

const explanationTranslations = {
  total: t(`common.total`),
  of: t(`uiStats.of`),
};

const statValuesReservation = computed(() =>
  getStatValues({
    brands: reservation.value,
    previousBrands: previousReservation.value,
    title: t(`uiStats.title.reservation`),
    icon: DocumentMagnifyingGlassIcon,
    explanationTranslations,
  })
);

const statValuesScan = computed(() =>
  getStatValues({
    brands: scan.value,
    previousBrands: previousScan.value,
    title: t(`uiStats.title.scan`),
    icon: IdentificationIcon,
    explanationTranslations,
  })
);

const statValuesCheckin = computed(() =>
  getStatValues({
    brands: checkin.value,
    previousBrands: previousCheckin.value,
    title: t(`uiStats.title.checkin`),
    icon: BuildingStorefrontIcon,
    explanationTranslations,
  })
);

const deepClone = (obj: any) => {
  if (obj === null || typeof obj !== "object") return obj;
  if (obj instanceof Date) return new Date(obj.getTime());
  if (Array.isArray(obj)) return obj.map(deepClone);

  const cloned: any = {};
  for (const key in obj) {
    if (Object.prototype.hasOwnProperty.call(obj, key)) {
      cloned[key] = deepClone(obj[key]);
    }
  }
  return cloned;
};

const getComparisonGlobal = async (range: DateRangeType) => {
  if (!range || range === "") {
    return;
  }

  try {
    comparisonDateRanges.globalRange = "";
    renderComponent.value = false;

    comparisonDateRanges.reservation = deepClone(range);
    comparisonDateRanges.scan = deepClone(range);
    comparisonDateRanges.checkin = deepClone(range);

    await getRatioData({ range });
    await getChartOptions();

    comparisonDateRanges.globalRange = deepClone(range);

    await nextTick();
    renderComponent.value = true;
  } catch (error) {
    console.error("Error in getComparisonGlobal:", error);
  }
};

const getSingleChartData = async (
  event: uiChartSelectedRangeEvent
): Promise<void> => {
  await getRatioData({ range: event.range, name: event.chartId });
  const clonedRange = deepClone(event.range);
  comparisonDateRanges[event.chartId] = clonedRange;
};

const getRatioData = async (
  options: {
    range?: DateRangeType;
    name?: keyof BrandRatioObject;
  } = {}
): Promise<void> => {
  try {
    const { range, name } = options;
    const brands = brandIdsSelected.value.length
      ? brandIdsSelected.value
      : brandIds.value;

    await comparisonStore.getComparisonStats({ brandIds: brands, range, name });

    getChartOptions();
  } catch (error) {
    console.error("Error in getRatioData:", error);
  }
};

const getChartOptions = async (): Promise<void> => {
  brandsReservation.value = brandIdsSelected.value.length
    ? brandIdsSelected.value
    : brandsReservation.value;

  brandsScan.value = brandIdsSelected.value.length
    ? brandIdsSelected.value
    : brandsScan.value;

  brandsCheckin.value = brandIdsSelected.value.length
    ? brandIdsSelected.value
    : brandsCheckin.value;
  const reservationNames = getBrandNames(
    brandsReservation.value,
    originalBrands,
    reservation.value
  );
  const scanNames = getBrandNames(brandsScan.value, originalBrands, scan.value);
  const checkinNames = getBrandNames(
    brandsCheckin.value,
    originalBrands,
    checkin.value
  );

  const newReservationOptions = getOptions(reservationNames);
  const newScanOptions = getOptions(scanNames);
  const newCheckinOptions = getOptions(checkinNames);

  if (newReservationOptions && typeof newReservationOptions === "object") {
    optionsReservation.value = {
      ...optionsReservation.value,
      ...newReservationOptions,
    };
  }
  if (newScanOptions && typeof newScanOptions === "object") {
    optionsScan.value = { ...optionsScan.value, ...newScanOptions };
  }
  if (newCheckinOptions && typeof newCheckinOptions === "object") {
    optionsCheckin.value = { ...optionsCheckin.value, ...newCheckinOptions };
  }
};

const uiCheckBoxHandleClick = (event: uiCheckBoxEvent): void => {
  const isChecked = event.checked;
  const brandId = parseInt(event.value);
  const brand = uiCheckBoxItem.value.find(
    (brand) => Number(brand.id) === brandId
  );

  if (isChecked) {
    if (!brandIdsSelected.value.includes(brandId)) {
      brandIdsSelected.value.push(brandId);
      brand && (brand.check = true);
    }
  } else {
    const index = brandIdsSelected.value.indexOf(brandId);
    if (index !== -1) {
      brandIdsSelected.value.splice(index, 1);
      brand && (brand.check = false);
    }
  }
};

const updateHeight = async (): Promise<void> => {
  renderComponent.value = false;
  const selectedBrandIds = brandIdsSelected.value.length
    ? brandIdsSelected.value
    : brandIds.value;
  uiChartHeight.value = await calculateHeight(selectedBrandIds.length);
  await nextTick();
  renderComponent.value = true;
};

const handleCloseRightSideBar = async (): Promise<void> => {
  openRightSidebar.value = false;
  if (
    comparisonDateRanges.globalRange &&
    comparisonDateRanges.globalRange !== ""
  ) {
    await getComparisonGlobal(comparisonDateRanges.globalRange);
  }
  await updateHeight();
};

const handleOpenRightSideBar = (): void => {
  uiCheckBoxItem.value.forEach((brand) => {
    if (!brandIdsSelected.value.length) {
      brand.check = false;
    } else {
      if (brandIdsSelected.value.includes(Number(brand.id))) {
        brand.check = true;
      }
    }
  });
  openRightSidebar.value = true;
};

const handleResetCheckedBrands = (): void => {
  uiCheckBoxItem.value.forEach((brand) => (brand.check = false));
  brandIdsSelected.value = [];
};
</script>
<template>
  <div>
    <uiRightSidebar
      :open="openRightSidebar"
      :title="t('comparison.uiRightSidebar.title')"
      :description="t('comparison.uiRightSidebar.description')"
      @close-right-bar="handleCloseRightSideBar"
    >
      <header class="flex justify-between mb-4 items-center">
        <h2 class="font-bold">{{ t("comparison.filter.brand") }}</h2>
        <span
          class="text-red-600 hover:text-red-900 cursor-pointer text-xs"
          @click="handleResetCheckedBrands"
          >{{ t("comparison.filter.restore") }}</span
        >
      </header>
      <uiCheckbox
        v-for="item in uiCheckBoxItem"
        :key="item.id"
        class="mb-2"
        :loading="false"
        :check="item.check"
        :value="item.id"
        @checkbox-changed="uiCheckBoxHandleClick"
      >
        {{ item.name }}
      </uiCheckbox>
    </uiRightSidebar>
    <div
      class="actionsAndFilters flex flex-col lg:flex-row w-full justify-between mb-12"
    >
      <div class="flex flex-col lg:flex-row items-center">
        <DateRange
          :global_range="comparisonDateRanges.globalRange"
          @range-value="getComparisonGlobal"
        />
      </div>
      <div class="flex-col items-center">
        <uiButton
          class="w-full mt-4 lg:w-auto lg:mt-0 lg:ml-4"
          :icon="FunnelIcon"
          :loading="comparisonDateRanges.globalRange ? false : true"
          @click="handleOpenRightSideBar"
          >{{ t("comparison.filter.stats") }}</uiButton
        >
      </div>
    </div>
    <div class="grid grid-cols-1 lg:grid-cols-3 gap-4">
      <uiStats
        :loading="
          reservation.length && previousReservation.length ? false : true
        "
        :item="statValuesReservation"
      />
      <uiStats
        :loading="scan.length && previousScan.length ? false : true"
        :item="statValuesScan"
      />
      <uiStats
        :loading="checkin.length && previousCheckin.length ? false : true"
        :item="statValuesCheckin"
      />
    </div>
    <div class="stats grid grid-cols-1 gap-4 mt-4">
      <uiChart
        id="reservation"
        :height="uiChartHeight"
        :title="t('comparison.uiChart.title.reservation')"
        :options="optionsReservation"
        :series="chartReservationSerie"
        :loading="reservation.length ? false : true"
        :horizontal="true"
        :stacked="true"
        :range="comparisonDateRanges.reservation"
        :dates="
          comparisonDateRanges.reservation.from ||
          comparisonDateRanges.reservation.to
            ? comparisonDateRanges.reservation
            : null
        "
        type="bar"
        @selected-range="getSingleChartData"
        @data-point-selection="(eventData:uiChartPointSelection) => redirectFromComparisonToTable
        (eventData, 'reservations', comparisonDateRanges.reservation, brandsReservation)"
      />
    </div>
    <div
      class="stats grid grid-cols-1 lg:grid-cols-1 xl:grid-cols-2 gap-4 mt-4"
    >
      <uiChart
        id="scan"
        :height="uiChartHeight"
        :title="t('comparison.uiChart.title.scan')"
        :options="optionsScan"
        :series="chartScanSerie"
        :loading="scan.length ? false : true"
        :horizontal="true"
        :stacked="true"
        :range="comparisonDateRanges.scan"
        :dates="
          comparisonDateRanges.scan.from || comparisonDateRanges.scan.to
            ? comparisonDateRanges.scan
            : null
        "
        type="bar"
        @selected-range="getSingleChartData"
        @data-point-selection="(eventData:uiChartPointSelection) => redirectFromComparisonToTable
        (eventData, 'scans', comparisonDateRanges.scan, brandsScan)"
      />
      <uiChart
        id="checkin"
        :height="uiChartHeight"
        :title="t('comparison.uiChart.title.checkin')"
        :options="optionsCheckin"
        :series="chartCheckinSerie"
        :loading="checkin.length ? false : true"
        :horizontal="true"
        :stacked="true"
        :range="comparisonDateRanges.checkin"
        :dates="
          comparisonDateRanges.checkin.from || comparisonDateRanges.checkin.to
            ? comparisonDateRanges.checkin
            : null
        "
        type="bar"
        @selected-range="getSingleChartData"
        @data-point-selection="(eventData:uiChartPointSelection) => redirectFromComparisonToTable
        (eventData, 'precheckins', comparisonDateRanges.checkin, brandsCheckin)"
      />
    </div>
  </div>
</template>

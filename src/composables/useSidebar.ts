import { useUserStore } from "@/stores/user";
import { ref, watch } from "vue";
import type { Ref, FunctionalComponent, HTMLAttributes, VNodeProps } from "vue";
import { useRoute, useRouter } from "vue-router";
import { useBrandStore } from "../stores/brand";
import { useComparisonStore } from "@/stores/comparison";

interface SidebarElement {
	name: string;
	icon?: string | FunctionalComponent<HTMLAttributes & VNodeProps, {}>;
	current?: boolean;
	id?: string;
	color?: string;
	routeName?: string;
}

export const useSidebar = (sideBarItems: Array<SidebarElement>) => {
	const router = useRouter();
	const route = useRoute();
	const userStore = useUserStore();
	const brandStore = useBrandStore();
	const comparisonStore = useComparisonStore();

	const sidebarElements: Array<SidebarElement> = sideBarItems;

	const sidebar: Ref<Array<SidebarElement>> = ref(sidebarElements);

	// we watch the change in routing, if the route name is the same as the sidebar item id then we update the current attribute to true
	watch(
		() => ({
			name: route.name,
		}),
		(route) => {
			sidebar.value = sidebar.value.map((item) => {
				if (item.id === route.name) {
					item.current = true;
				} else {
					item.current = false;
				}
				return item;
			});
		},
		{
			immediate: true,
		},
	);

	const cleanQueryParamsAndReload = async () => {
		await router.replace({
			path: router.currentRoute.value.path,
			replace: true,
		});
	};

	const sidebarActions = async (routeName: string, brand_id?: string) => {

		const currentRoute = router.currentRoute.value;
		const isInUserManagement = currentRoute.name === "userManagement" || currentRoute.name === "createUser";
		const routesRequiringAccountId = ["brands", "comparison", "overview", "reservations", "scans", "precheckins", "dashboard"];

		if (isInUserManagement && routesRequiringAccountId.includes(routeName)) {
			const lastAccountId = localStorage.getItem('lastAccountId');
			if (lastAccountId) {
				switch (routeName) {
					case "dashboard":
					case "overview":
					case "brands":
						router.push({ name: "brands", params: { account_id: lastAccountId } });
						break;
					case "reservations":
						router.push({ name: "reservations", params: { account_id: lastAccountId, brand_id: localStorage.getItem('lastBrandId') || '1' } });
						break;
					case "scans":
						router.push({ name: "scans", params: { account_id: lastAccountId, brand_id: localStorage.getItem('lastBrandId') || '1' } });
						break;
					case "precheckins":
						router.push({ name: "precheckins", params: { account_id: lastAccountId, brand_id: localStorage.getItem('lastBrandId') || '1' } });
						break;
					case "comparison":
						router.push({ name: "comparison", params: { account_id: lastAccountId } });
						break;
					default:
						router.push({ name: "accounts" });
						break;
				}
			} else {
				router.push({ name: "accounts" });
			}
			return;
		}

		switch (routeName) {
			case "logout":
				await userStore.logout();
				await brandStore.reset();
				await comparisonStore.reset();
				router.push({ name: "login" });
				break;
			case "userManagement":
				if (router.currentRoute.value.name === "userManagement") {
					await cleanQueryParamsAndReload();
				}
				router.push({ name: "userManagement" });
				break;
			case "logo":
				if (router.currentRoute.value.name === "brands") {
					await cleanQueryParamsAndReload();
				}
				router.push({ name: "accounts" });
				break;
			case "comparison":
				if (router.currentRoute.value.name === routeName) {
					await cleanQueryParamsAndReload();
				}
				router.push({ name: "comparison", params: { account_id: brand_id } });
				break;
			default:
				if (router.currentRoute.value.name === routeName) {
					await cleanQueryParamsAndReload();
				}
				if (!brand_id) {
					throw new Error(`Missing required param "brand_id" for route "${routeName}"`);
				}
				router.push({ name: routeName, params: { brand_id } });
				break;
		}
	};

	return { sidebar, sidebarActions };
};

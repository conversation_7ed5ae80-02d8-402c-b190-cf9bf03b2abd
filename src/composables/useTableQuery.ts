import { ref } from "vue";
import { useRouteQuery } from "@vueuse/router";

import type { Ref } from "vue";




const items = [
	{
		id: 1,
		row: [
			"Main Switch",
			"Switch",
			"UAP-AC-Mesh-Pro",
			"Garden pool",
			"10.100.0.1",
			"38:1B:A0:6F:00:CD",
			"Management",
			{ content: "DONE", color: "success", type: "tag" },
			{ content: "ALIVE", color: "success", type: "tag" },
			"",
			"1 day ago",
		],
	},
	{
		id: 2,
		row: [
			"Ap 5",
			"Access point",
			"UAP-AC-Pro",
			"Garden pool",
			"10.100.0.1",
			"38:1B:A0:6F:00:CD",
			"Management",
			{ content: "DONE", color: "success", type: "tag" },
			{ content: "ALIVE", color: "success", type: "tag" },
			"",
			"1 day ago",
		],
	},
	{
		id: 3,
		row: [
			"Main Router",
			"Router",
			"RB1100",
			"Reception desk",
			"10.100.254.1",
			"38:1B:A0:6F:00:CD",
			"Management",
			{ content: "DONE", color: "success", type: "tag" },
			{ content: "DEAD", color: "danger", type: "tag" },
			{ content: "YES", color: "danger", type: "tag" },
			"5 minutes ago",
		],
	},
	{
		id: 4,
		row: [
			"Main Router",
			"Router",
			"RB1100",
			"Reception desk",
			"10.100.254.1",
			"38:1B:A0:6F:00:CD",
			"Management",
			{ content: "DONE", color: "success", type: "tag" },
			{ content: "ALIVE", color: "success", type: "tag" },
			"",
			"5 minutes ago",
		],
	},
	{
		id: 5,
		row: [
			"Main Router",
			"Router",
			"RB1100",
			"Reception desk",
			"10.100.254.1",
			"38:1B:A0:6F:00:CD",
			"Management",
			{ content: "DONE", color: "success", type: "tag" },
			{ content: "ALIVE", color: "success", type: "tag" },
			"",
			"5 minutes ago",
		],
	},
	{
		id: 6,
		row: [
			"Main Router",
			"Router",
			"RB1100",
			"Reception desk",
			"10.100.254.1",
			"38:1B:A0:6F:00:CD",
			"Management",
			{ content: "DONE", color: "success", type: "tag" },
			{ content: "ALIVE", color: "success", type: "tag" },
			"",
			"5 minutes ago",
		],
	},
	{
		id: 7,
		row: [
			"Main Router",
			"Router",
			"RB1100",
			"Reception desk",
			"10.100.254.1",
			"38:1B:A0:6F:00:CD",
			"Management",
			{ content: "DONE", color: "success", type: "tag" },
			{ content: "ALIVE", color: "success", type: "tag" },
			"",
			"5 minutes ago",
		],
	},
	{
		id: 8,
		row: [
			{ content: "main Router", type: "link", emits: "goToDevice" },
			"Router",
			"RB1100",
			"Reception desk",
			"10.100.254.1",
			"38:1B:A0:6F:00:CD",
			"Management",
			{ content: "DONE", color: "success", type: "tag" },
			{ content: "BAD", color: "warning", type: "tag" },
			{ content: "YES", color: "danger", type: "tag" },
			"5 minutes ago",
		],
	},
	{
		id: 9,
		row: [
			"Main Router",
			"Router",
			"RB1100",
			"Reception desk",
			"10.100.254.1",
			"38:1B:A0:6F:00:CD",
			"Management",
			{ content: "DONE", color: "success", type: "tag" },
			{ content: "ALIVE", color: "success", type: "tag" },
			"",
			"5 minutes ago",
		],
	},
	{
		id: 10,
		row: [
			"Main Router",
			"Router",
			"RB1100",
			"Reception desk",
			"10.100.254.1",
			"38:1B:A0:6F:00:CD",
			"Management",
			{ content: "PENDING", color: "warning", type: "tag" },
			"",
			"",
			"5 minutes ago",
		],
	},
];

const pageItemsArray = [
	{ name: "10", active: false, value: 10 },
	{ name: "25", active: false, value: 25 },
	{ name: "50", active: false, value: 50 },
	{ name: "100", active: false, value: 100 },
];

export function useTableQuery(
	_endpoint: string,
	filtersArray: { name: string; value: string; active?: boolean }[],
) {
	// use useRouteQuery https://vueuse.org/router/useRouteQuery/
	// to sync the query params with local state
	const searchBy = useRouteQuery("search_by", "");
	const searchValue = useRouteQuery("search_value", "");
	const page = useRouteQuery("page", "1", { transform: Number });
	const pageItems = useRouteQuery("page_items", "50", { transform: Number });





	// rome-ignore lint/suspicious/noExplicitAny: <explanation>
	const tableData: Ref<Array<any>> = ref([]);
	const isLoading = ref(false);

	// add the active property to the filters passed
	// this is using the searchBy query param
	const filters = filtersArray.map((i) => {
		if (i.name === searchBy.value) {
			return { ...i, active: true };
		} else {
			return i;
		}
	});

	// same as above for pageItemsOptions
	const pageItemsOptions = ref(
		pageItemsArray.map((i) => {
			if (i.value === pageItems.value) {
				return { ...i, active: true };
			} else {
				return i;
			}
		}),
	);

	async function fetchData() {
		isLoading.value = true;

		//we get the active filter and page from the lists
		const filter = filters.find((i) => i.active);

		// pageItems.value = pageItem.value || 10;
		if (filter && searchValue.value) {
			searchBy.value = filter.name;
		}

		// Example API call
		// const response = await API.get("stats", endpoint, {
		// 	queryStringParameters: {
		// 		filter: searchBy.value,
		// 		search: searchValue.value,
		// 	},
		// });

		return new Promise((resolve) => {
			setTimeout(() => {
				tableData.value = items;
				isLoading.value = false;
				return resolve({
					data: {},
				});
			}, 3000);
		});

		// Update the tableData with the fetched data
		// tableData.value = response.data;
		// isLoading.value = false;
	}

	// Call fetchData initially to fetch the data
	// fetchData();

	// Return the reactive variables and fetchData function
	return {
		tableData,
		isLoading,
		fetchData,
		searchBy,
		searchValue,
		filters,
		pageItemsOptions,
		page,
		lastPage: 10,
	};
}

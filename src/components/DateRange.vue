<template>
  <div class="flex justify-between items-center w-full gap-x-1 mr-2">
    <uiButton
      :color="globalDateRange === '24h' ? 'primary' : 'transparent'"
      :loading="globalDateRange ? false : true"
      @click="setDateRange('24h')"
      >24H</uiButton
    >
    <uiButton
      :color="globalDateRange === '7d' ? 'primary' : 'transparent'"
      :loading="globalDateRange ? false : true"
      @click="setDateRange('7d')"
      >7D</uiButton
    >
    <uiButton
      :color="globalDateRange === '1m' ? 'primary' : 'transparent'"
      :loading="globalDateRange ? false : true"
      @click="setDateRange('1m')"
      >1M</uiButton
    >
    <uiButton
      :color="globalDateRange === '3m' ? 'primary' : 'transparent'"
      :loading="globalDateRange ? false : true"
      @click="setDateRange('3m')"
      >3M</uiButton
    >
  </div>
  <uiDateRange
    id="range"
    :literals="{
      from: t('overview.uiDateRange.from'),
      to: t('overview.uiDateRange.to'),
      search: t('overview.uiDateRange.search'),
    }"
    :loading="globalDateRange ? false : true"
    :values="dateRange"
    class="my-4 lg:my-0"
    @ui-date-range-button-clicked="updateDateRange"
  />
</template>
<script setup lang="ts">
import type { DateRangeType } from "@/types";
import { onMounted, watch, ref } from "vue";
import { validateDateRange } from "@/helpers/dates";
import { isAbsoluteDateRange } from "@/helpers/uiDateRange";

import { t } from "@/locales";

interface DateRangeProps {
  global_range: DateRangeType;
}

type DateRangeColor = "danger" | "normal";

const props = defineProps<DateRangeProps>();
const globalDateRange = ref(props.global_range);
const absoluteValues = ref<DateRangeType>("24h");
const color = ref<DateRangeColor>("normal");

onMounted(async (): Promise<void> => {
  if (!props.global_range) {
    setDateRange("24h");
  } else {
    setDateRange(props.global_range);
  }
});

watch(
  () => props.global_range,
  (newValue) => {
    globalDateRange.value = newValue;
  }
);

const emit = defineEmits(["range-value"]);

const setDateRange = (range: DateRangeType) => {
  absoluteValues.value = range;

  let isValidDateRange = true;
  if (isAbsoluteDateRange(range)) {
    isValidDateRange = validateDateRange(range);
  }

  if (!isValidDateRange) {
    color.value = "danger";
  } else {
    try {
      color.value = "normal";
      emit("range-value", range);
      return Promise.resolve();
    } catch (error) {
      return Promise.reject();
    }
  }
};

const dateRange = ref({ from: "", to: "" });

const updateDateRange = (eventData: { from: string; to: string }) => {
  dateRange.value = { from: eventData.from, to: eventData.to };

  const absoluteDateRange: DateRangeType = {
    from: eventData.from,
    to: eventData.to,
  };

  setDateRange(absoluteDateRange);
};
</script>

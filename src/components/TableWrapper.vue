<template>
  <div v-show="!items || items.length !== 0">
    <div
      class="actionsAndFilters mb-6 px-1 flex flex-col xl:flex-row items-center justify-between"
    >
      <div
        class="table-filter flex flex-col lg:flex-row items-center w-full xl:w-1/2"
      >
        <uiFilter
          data-test="ui-filter"
          position="left"
          :items="filters"
          :loading="items ? false : true"
        />
        <uiInput
          data-test="ui-input"
          class="ml-2 grow"
          name="filterInput"
          :value="searchValue"
          :loading="items ? false : true"
          :right-add-on="t('filterTable.delete')"
          @input-changed="searchValue = $event.value"
          @right-add-on-clicked="deleteSearchFilters"
        />
        <uiButton
          data-test="ui-filterButton"
          class="ml-2"
          :loading="items ? false : true"
          @click="updateSearchFilters"
        >
          {{ t("filterTable.filterButton") }}
        </uiButton>
      </div>
      <div
        class="flex flex-row mt-4 xl:mt-0 xl:ml-2 w-full xl:w-1/2 justify-end"
      >
        <uiButton
          v-if="checkbox"
          data-test="ui-createUserButton"
          class="w-full lg:w-auto mt-2"
          :loading="items ? false : true"
          :icon="createButtonIcon"
          @click="$emit('btn-action')"
        >
          {{ createButtonText }}
        </uiButton>
      </div>
    </div>
    <uiTable
      data-test="ui-table"
      :header="header"
      :items="items"
      :ordered-by="sortField"
      :order-direction="sortOrder"
      :loading="items ? false : true"
      :actions="
        checkbox
          ? [
              { name: 'Edit', id: 'edit' },
              { name: 'Delete', id: 'delete' },
            ]
          : []
      "
      @table-action="tableAction"
      @order-by="updateOrder"
      @show-message-modal="showMessageModal($event)"
    />
    <uiModal
      modal-name="errorMessageModal"
      :title="t('errorMessageModal.title')"
      :actions="[{ value: 'close', name: t('errorMessageModal.closeButton') }]"
      :open="isMessageModalShown"
      type="danger"
      @modal-action="modalActions($event)"
    >
      <p class="mb-4">{{ modalMessage }}</p>
    </uiModal>
    <div class="px-1 flex flex-row justify-between items-center">
      <uiDropdown
        data-test="ui-dropdown"
        :items="perPageOptions"
        :loading="items ? false : true"
        @option-selected="updatePerPage(Number($event.value))"
      />
      <uiPagination
        data-test="ui-pagination"
        class="grow ml-10"
        :current="page"
        :total="last_page"
        :loading="items ? false : true"
        @change-page="updatePage"
      >
        <template #previous>{{ t("filterTable.previous") }}</template>
        <template #next>{{ t("filterTable.next") }}</template>
        <template #showing>{{ t("filterTable.showing") }}</template>
        <template #of>{{ t("filterTable.of") }}</template>
      </uiPagination>
    </div>
  </div>
  <div v-show="items?.length === 0">
    <uiNoResults
      :title="t('filterTable.noResults.title')"
      :message="t('filterTable.noResults.body')"
      :actions="[
        { action: 'back', text: t('filterTable.noResults.back') },
        { action: 'home', text: t('filterTable.noResults.home') },
      ]"
      @action="actionNoResults($event)"
    />
  </div>
</template>

<script setup lang="ts">
import { useRouteQuery } from "@vueuse/router";
import type {
  SearchFilters,
  TableWrapperProps,
  TableActionEvent,
} from "@/types";
import { type uiModalEvent } from "@/uiTypes";
import {
  transformErrorValue,
  getErrorCode,
  transformDocumentTypeValue,
  transformSourceValue,
  transformManualValue,
} from "@/helpers/searchValue";
import { t } from "@/locales";
import { ref, onMounted, watch } from "vue";

const props = defineProps<TableWrapperProps>();
const emit = defineEmits([
  "filters-updated",
  "edit",
  "delete",
  "btn-action",
  "back",
  "home",
]);

// use useRouteQuery https://vueuse.org/router/useRouteQuery/
// to sync the query params with local state
const dateFrom = useRouteQuery("date_from", "", { transform: String });
const dateTo = useRouteQuery("date_to", "", { transform: String });
const searchBy = useRouteQuery("search_by", "", { transform: String });
const searchValue = useRouteQuery("search_value", "", { transform: String });
const page = useRouteQuery("page", "1", { transform: Number });
const perPage = useRouteQuery("per_page", "10", { transform: Number });
const sortField = useRouteQuery("sort_field", props.ordered_by, {
  transform: String,
});
const sortOrder = useRouteQuery("sort_order", props.order_direction, {
  transform: String,
});

const perPageArray = [
  { name: "10", active: false, value: 10 },
  { name: "25", active: false, value: 25 },
  { name: "50", active: false, value: 50 },
  { name: "100", active: false, value: 100 },
];

const isMessageModalShown = ref(false);
const modalMessage = ref("");

onMounted(async () => {
  emitFiltersUpdated();
});

const updateFilters = (searchByValue: string) => {
  const updatedFilters = props.filters.map((i) => {
    if (i.value === searchByValue) {
      return { ...i, active: true };
    } else {
      return { ...i, active: false };
    }
  });
  updatedFilters.unshift({
    name: t("filterTable.filterBy"),
    value: "",
    active: true,
  });
  return updatedFilters;
};

const filters = updateFilters(searchBy.value);

// same as above for perPageOptions
const perPageOptions = ref(
  perPageArray.map((i) => {
    if (i.value === perPage.value) {
      return { ...i, active: true };
    } else {
      return i;
    }
  })
);

const searchByMap = {
  error_code: () => getErrorCode(searchValue.value),
  error: () => transformErrorValue(searchValue.value),
  document_type: () => transformDocumentTypeValue(searchValue.value),
  source: () => transformSourceValue(searchValue.value),
  manual: () => transformManualValue(searchValue.value),
};
//in case the page selected was other than 1, if "searchValue" change, we set the "page" to 1 so that the API returns the reservatiosn with the new filters from the first page
const emitFiltersUpdated = () => {
  let updatedFilters: SearchFilters = {
    date_from: dateFrom.value,
    date_to: dateTo.value,
    search_by: searchBy.value,
    search_value: searchByMap[searchBy.value]?.() ?? searchValue.value,
    sort_field: sortField.value,
    sort_order: sortOrder.value,
    page: page.value,
    per_page: perPage.value,
  };
  // emit table updated event to parent component every time the data updated
  emit("filters-updated", updatedFilters);
};

//in case the page selected was other than 1, if "perPage" or "searchBy" change, we set the "page" to 1 so that the API returns the reservations with the new filters from the first page

watch([searchBy, page, perPage, dateFrom], (newValues, oldValues) => {
  const [newSearchBy, newPage, newPerPage] = newValues;
  const [oldSearchBy] = oldValues;

  const isPerPageChanged = newPerPage !== oldValues[2];
  const isSearchByChanged = newSearchBy !== oldSearchBy;

  let updatedFilters: SearchFilters = {
    date_from: dateFrom.value,
    date_to: dateTo.value,
    search_by: newSearchBy,
    search_value: searchByMap[searchBy.value]?.() ?? searchValue.value,
    per_page: newPerPage,
    page: isPerPageChanged || isSearchByChanged ? 1 : newPage,
    sort_field: sortField.value,
    sort_order: sortOrder.value,
  };
  page.value = updatedFilters.page ?? 1;
  // emit table updated event to parent component every time the data updated
  emit("filters-updated", updatedFilters);
});

const findActiveFilter = () => {
  return filters.find((i) => i.active);
};
// update search value since we dont use models for the IUI
const updateSearchFilters = () => {
  const filter = findActiveFilter();
  if (filter && searchValue.value) {
    // manually emit the filters if the search_by filter hasnt changed
    // else update the filter search_by ref which will be catched by the watch
    if (searchBy.value === filter.value) {
      emitFiltersUpdated();
    } else {
      searchBy.value = filter.value;
    }
  }
};

const deleteSearchFilters = () => {
  const filter = findActiveFilter();
  if (filter || searchValue.value) {
    searchBy.value = "";
    searchValue.value = "";
    sortField.value = props.ordered_by;
    sortOrder.value = props.order_direction;
    filter ? (filter.active = true) : null;
  }
};

const updateOrder = (order: { value: string; orderDirection: string }) => {
  sortField.value = order.value;
  sortOrder.value = order.orderDirection;
  emitFiltersUpdated();
};

// update the reactive perPage with correct active item
// same as above , would be so much easier to be able to use a model
// TODO : use models in IUI instead of this
const updatePerPage = (e: number) => {
  perPageOptions.value = perPageOptions.value.map((i) => {
    if (i.value === e) {
      return { ...i, active: true };
    } else {
      return { ...i, active: false };
    }
  });

  // update the perPage with the value
  perPage.value = e;
};

// TODO : use models in IUI instead of this
const updatePage = (e: number) => {
  page.value = e;
};

const backPageReload = () => {
  searchBy.value = "";
  searchValue.value = "";
  sortField.value = props.ordered_by;
  sortOrder.value = props.order_direction;
  const filter = findActiveFilter();
  filter ? (filter.active = true) : null;
  emitFiltersUpdated();
};

const actionNoResults = (action: string) => {
  switch (action) {
    case "back":
      backPageReload();
      break;
    case "home":
      emit("home");
      break;
    default:
      break;
  }
};

const showMessageModal = (event: Event) => {
  const selectedItem = props.items?.find((item) => item.id === event.id);

  if (selectedItem) {
    const rowArray = selectedItem.row;
    if (rowArray[3] && Object.hasOwn(rowArray[3], "message")) {
      modalMessage.value = rowArray[3].message;
    }
  }

  isMessageModalShown.value = true;
};

const tableAction = (e: TableActionEvent) => {
  switch (e.action) {
    case "delete":
      emit("delete", e);
      break;
    case "edit":
      emit("edit", e);
      break;
    default:
      break;
  }
};

const modalActions = async (event: uiModalEvent): Promise<void> => {
  switch (event.modal) {
    case "errorMessageModal":
      if (event.action === "close") {
        isMessageModalShown.value = false;
      }
      break;
    default:
      break;
  }
};
</script>

<style scoped></style>

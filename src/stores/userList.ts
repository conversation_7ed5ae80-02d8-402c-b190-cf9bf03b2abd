import { defineS<PERSON> } from "pinia";
import usersRepository from "@/modules/Users/<USER>";
import type { SearchFilters, UserData } from "@/types";

interface State {
	items: UserData[] | null;
	filteredItems: UserData[] | null;
	last_page: number | null;
	filters: SearchFilters;
}

export const useUserListStore = defineStore("usersPool", {
	state: (): State => {
		return {
			items: null,
			filteredItems: null,
			last_page: null,
			filters: {
				date_from: "",
				date_to: "",
				search_by: "",
				search_value: "",
				per_page: 10,
				page: 1,
				sort_field: "",
				sort_order: "",
			},
		};
	},
	actions: {
		async getUsers(filters: SearchFilters, state: State) {
			try {
				let users: UserData[] | null;
				users = state.items ?? null;
				state.filters = filters;
				if (!users) {
					const UsersRepository = new usersRepository();
					await UsersRepository.init();
					users = await UsersRepository.listUsersWithGroups();
					this.items = users;
				}

				if (
					filters.search_by &&
					filters.search_value !== null &&
					users !== null
				) {
					const searchValue = filters.search_value ?? "";
					users = users.filter((user: UserData) => {
						return user[filters.search_by].includes(searchValue);
					});
				}

				if (filters.sort_order && filters.sort_field && users !== null) {
					users.sort((a: UserData, b: UserData) => {
						if (filters.sort_order === "asc") {
							return a[filters.sort_field] > b[filters.sort_field] ? 1 : -1;
						} else {
							return a[filters.sort_field] < b[filters.sort_field] ? 1 : -1;
						}
					});
				}

				const startIndex = (filters.page - 1) * filters.per_page;
				const endIndex = startIndex + filters.per_page;

				const total = users?.length;
				this.last_page = Math.ceil(total / filters.per_page);
				this.filteredItems =
					users !== null ? users.slice(startIndex, endIndex) : [];
				return Promise.resolve();
			} catch (error) {
				console.error("Failed to fetch user list", error);
				return Promise.reject();
			}
		},
		async refreshUsers() {
			this.items = null;
			this.filteredItems = null;
		},
		async createUser(
			email: string,
			group: string,
			brandId: string,
			mfa: boolean,
		) {
			try {
				const UsersRepository = new usersRepository();
				await UsersRepository.init();
				const user = await UsersRepository.createUser({
					email,
					group,
					brandId,
					mfa,
				});
				await this.refreshUsers();
				return user;
			} catch (error) {
				console.error("Failed to create user", error);
				throw error;
			}
		},
		async deleteUsers(userIndexes: number[]) {
			try {
				const userIdsToRemove = this.filteredItems
					?.filter((user: UserData, index: number) =>
						userIndexes.includes(index),
					)
					.map((user: UserData) => user.id);
				if (!userIdsToRemove) {
					console.error("No user IDs found for the given indexes", userIndexes);
					return;
				}
				for (const userId of userIdsToRemove) {
					const UsersRepository = new usersRepository();
					await UsersRepository.init();
					await UsersRepository.removeUser(userId);
				}
				await this.refreshUsers();
				return true;
			} catch (error) {
				console.error("Failed to delete users", error);
				throw error;
			}
		},
		async editUsers(
			userIndexes: number[],
			newGroup: string,
			updatedBrands: string,
			newMfaRequired: boolean,
		) {
			try {
				const userIdsToEdit = this.filteredItems
					?.filter((user: UserData, index: number) =>
						userIndexes.includes(index),
					)
					.map((user: UserData) => user.id);
				if (!userIdsToEdit) {
					console.error("No user IDs found for the given indexes", userIndexes);
					return;
				}
				for (const userId of userIdsToEdit) {
					let currentGroup = "";
					let currentBrands = "";
					let currentMfaRequired = "";
					this.filteredItems?.forEach((user: UserData) => {
						if (user.id === userId) {
							currentGroup = user.group;
							currentBrands = user.brand_ids;
							currentMfaRequired = user.mfa_required;
						}
					});
					const UsersRepository = new usersRepository();
					await UsersRepository.init();
					// Update group
					if (newGroup !== currentGroup && newGroup !== "") {
						await UsersRepository.updateGroup(userId, currentGroup, newGroup);
					}
					// Update MFA
					const newMfa = newMfaRequired.toString();
					if (newMfa !== currentMfaRequired) {
						await UsersRepository.updateMfa(userId, newMfa);
					}
					// Update brands
					if (updatedBrands === "") {
						await UsersRepository.updateBrands(userId, updatedBrands);
					} else if (updatedBrands !== currentBrands) {
						console.log("Updating brands from", currentBrands, "to", updatedBrands);
						///updatedBrands = updatedBrands.replace(/\s/g, "").replace(/[^,\d]/g, "");
						let brandIdsSplited: Array<string>;
						if (updatedBrands.includes(",")) {
							brandIdsSplited = updatedBrands.split(",");
						} else {
							brandIdsSplited = [updatedBrands];
						}
						console.log("Brand IDs to validate:", brandIdsSplited);
						const areBrandsValid = await UsersRepository.validateBrands(
							brandIdsSplited,
						);
						console.log("Validation result:", areBrandsValid);
						if (areBrandsValid) {
							await UsersRepository.updateBrands(userId, updatedBrands);
						} else {
							console.error("Invalid brand IDs provided", updatedBrands);
						}
					}
				}
				await this.refreshUsers();
				return true;
			} catch (error) {
				console.error("Failed to delete users", error);
				throw error;
			}
		},
	},
});

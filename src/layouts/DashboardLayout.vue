<script setup lang="ts">
import { t } from "@/locales";
import { useBreadcrumbs } from "@/composables/useBreadcrumbs";
import { useSidebar } from "@/composables/useSidebar";
import { useRoute, useRouter } from "vue-router";
import { useRouteQuery } from "@vueuse/router";
import { computed, provide } from "vue";
import { HOTELINKING_AUTOCHECKIN_LOGO, HOTELINKING_LOGO } from "@/constants";
import { storeToRefs } from "pinia";
import { useBrandStore } from "@/stores/brand";
import { useUserStore } from "@/stores/user";
import { brandsSideBarList } from "@/composables/sideBarLists";

const searchValue = useRouteQuery("search_value", "", { transform: String });

const route = useRoute();
const router = useRouter();
const brandStore = useBrandStore();
const userStore = useUserStore();
const { breadcrumbs } = useBreadcrumbs();

const { sidebar, sidebarActions } = useSidebar(brandsSideBarList);

const { getBrandById, brands } = storeToRefs(brandStore);

const brand = computed(() => {
  const brandId = Number(route.params.brand_id);
  return getBrandById.value(brandId);
});

const accountLogo = computed(() => {
  return brand.value?.logo ? brand.value.logo : HOTELINKING_LOGO;
});

const brandList = computed(() =>
  brands.value.map((b) => ({
    name: b.brandInfo?.name,
    id: b.brandInfo?.id,
  }))
);

const uiTopBarOptions = computed(() => {
  const isAdmin = userStore.isAdmin;
  const userManagementOption = {
    name: t("common.userManagement"),
    id: "userManagement",
  };
  const logoutOption = { name: t("common.logout"), id: "logout" };
  return isAdmin ? [userManagementOption, logoutOption] : [logoutOption];
});

const topbarOptions = computed(() => {
  return {
    logo: HOTELINKING_AUTOCHECKIN_LOGO,
    accountLogo: accountLogo.value,
    profileMenu: uiTopBarOptions.value,
    alerted: false,
    selectedItem: { name: brand.value?.name, id: brand.value?.id },
    selectItems: brandList.value,
  };
});

const isAccountsRoute = computed(() => {
  const currentRouteName = route?.name;
  return currentRouteName === "accounts" || currentRouteName === "brands";
});

const sidebarOptions = computed(() => {
  return {
    productsSidebar: [
      {
        name: "deskForce",
        icon: "https://images.hotelinking.com/ui/ImagoDeskForce.svg",
        active: true,
      },
      {
        name: "guestMaker",
        icon: "https://images.hotelinking.com/ui/ImagoGuestMaker.svg",
        active: false,
      },
      {
        name: "wifibot",
        icon: "https://images.hotelinking.com/ui/ImagoWiFiBot.svg",
        active: false,
      },
    ],
    navigation: sidebar.value,
    isFilterNavigation: isAccountsRoute.value,
    filterNavigation: {
      hasSearch: true,
      search: {
        loading: false,
        name: "hotelName",
        placeholder: t("brands.filter.placeholder"),
        label: t("brands.filter.label"),
      },
    },
  };
});

const pageTitle = computed(() => {
  return t(`title.${String(route.name)}`);
});

const filterInputChanged = async (event) => {
  searchValue.value = event.value;
};

const searchBarActions = async (event: { id: number | string }) => {
  if (!event.id) {
    return;
  }
  const brandObj = brands.value.find(
    (b) => b.brandInfo?.id === Number(event.id)
  );

  if (!brandObj) {
    return;
  }

  const currentRouteName = route.name;

  const targetRoute =
    currentRouteName === "accounts" || currentRouteName === "brands"
      ? "overview"
      : currentRouteName;

  await router.push({
    name: targetRoute,
    params: {
      account_id: brandObj.accountInfo?.id || brandObj.brandInfo?.id,
      brand_id: brandObj.brandInfo?.id,
    },
    query: route.query,
  });
};

const handleSidebarClick = async (routeName: string) => {
  let brandId = route.params.brand_id as string;

  if (!brandId && route.name === "comparison") {
    const accountId = Number(route.params.account_id);
    const accountBrands = brands.value.filter(
      (b) => b.accountInfo?.id === accountId || b.brandInfo?.id === accountId
    );

    if (accountBrands.length > 0) {
      brandId = accountBrands[0].brandInfo?.id?.toString();
    }
  }

  await sidebarActions(routeName, brandId);
};

provide("searchValue", searchValue);
</script>

<template>
  <uiWrapper
    :sidebar="sidebarOptions"
    :topbar="topbarOptions"
    @select-changed="searchBarActions"
    @side-bar-click="handleSidebarClick"
    @top-bar-click="handleSidebarClick"
    @input-changed="filterInputChanged"
  >
    <main class="flex flex-col w-full grow">
      <header class="mb-12">
        <div class="mx-auto flex flex-col">
          <div class="hidden sm:block">
            <uiBreadcrumbs
              :pages="breadcrumbs"
              :loading="breadcrumbs ? false : true"
              @bread-crumb-clicked="handleSidebarClick"
            />
          </div>
          <h1
            class="text-2xl font-bold leading-7 sm:text-4xl sm:truncate mt-6"
            data-test="page-title"
          >
            {{ pageTitle }}
          </h1>
        </div>
      </header>
      <router-view :key="brand?.id"></router-view>
    </main>
  </uiWrapper>
</template>
